import React, { useEffect, useRef, useState } from 'react';
import Tiles from '../tiles/Tiles';

interface PolygonPoint {
    x: number;
    y: number;
}
interface Polygon {
    points: PolygonPoint[];
}

interface ImageWithPolygonProps {
    mapData: any;
    imageUrl: string;
}

const ImageWithPolygon: React.FC<ImageWithPolygonProps> = ({
    mapData,
    imageUrl,
}) => {
    const [jsonData, setJsonData] = useState<any | null>(null);
    const [selectedProductName, setSelectedProductName] = useState<
        string | null
    >(null);
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch(mapData);
                const data = await response.json();
                setJsonData(data);
                console.log(data);
            } catch (error) {
                console.error('Błąd podczas pobierania danych:', error);
            }
        };

        fetchData();
    }, []);

    useEffect(() => {
        console.log('jsonData');
        console.log(jsonData);
        if (!jsonData) return;

        const canvas = canvasRef.current;
        const context = canvas?.getContext('2d');

        const updateCanvasSize = (imageWidth: number, imageHeight: number) => {
            const aspectRatio = imageWidth / imageHeight;
            const canvasWidth = Math.min(window.innerWidth, imageWidth);
            const canvasHeight = canvasWidth / aspectRatio;

            setCanvasSize({ width: canvasWidth, height: canvasHeight });
            return { canvasWidth, canvasHeight };
        };

        if (canvas && context) {
            const image = new Image();
            image.src = imageUrl;

            image.onload = () => {
                const { canvasWidth, canvasHeight } = updateCanvasSize(
                    image.width,
                    image.height,
                );

                canvas.width = canvasWidth;
                canvas.height = canvasHeight;

                context.drawImage(image, 0, 0, canvasWidth, canvasHeight);

                jsonData.forEach((object: any) => {
                    object.polygons.forEach((polygon: Polygon) => {
                        context.beginPath();
                        polygon.points.forEach(
                            (point: PolygonPoint, index: number) => {
                                const scaledX =
                                    (point.x / image.width) * canvasWidth;
                                const scaledY =
                                    (point.y / image.height) * canvasHeight;

                                if (index === 0) {
                                    context.moveTo(scaledX, scaledY);
                                } else {
                                    context.lineTo(scaledX, scaledY);
                                }
                            },
                        );
                        context.closePath();
                        context.lineWidth = 2;
                        context.strokeStyle = 'transparent';
                        context.stroke();
                    });
                });
            };

            const isPointInPolygon = (
                x: number,
                y: number,
                polygons: Polygon[],
            ): boolean => {
                for (const polygon of polygons) {
                    const points = polygon.points;
                    let inside = false;

                    for (
                        let i = 0, j = points.length - 1;
                        i < points.length;
                        j = i++
                    ) {
                        const xi = (points[i].x / image.width) * canvas.width,
                            yi = (points[i].y / image.height) * canvas.height;
                        const xj = (points[j].x / image.width) * canvas.width,
                            yj = (points[j].y / image.height) * canvas.height;

                        const intersect =
                            yi > y !== yj > y &&
                            x < ((xj - xi) * (y - yi)) / (yj - yi) + xi;
                        if (intersect) inside = !inside;
                    }

                    if (inside) {
                        return true;
                    }
                }
                return false;
            };

            const handleClick = (event: MouseEvent) => {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                jsonData.forEach((object: any) => {
                    if (isPointInPolygon(x, y, object.polygons)) {
                        setSelectedProductName(object.displayedName);
                    }
                });
            };

            const handleMouseMove = (event: MouseEvent) => {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                let isInAnyPolygon = false;

                jsonData.forEach((object: any) => {
                    if (isPointInPolygon(x, y, object.polygons)) {
                        isInAnyPolygon = true;
                    }
                });

                if (isInAnyPolygon) {
                    canvas.style.cursor = 'pointer';
                } else {
                    canvas.style.cursor = 'default';
                }
            };

            canvas.addEventListener('click', handleClick);
            canvas.addEventListener('mousemove', handleMouseMove);

            return () => {
                canvas.removeEventListener('click', handleClick);
                canvas.removeEventListener('mousemove', handleMouseMove);
            };
        }

        const handleResize = () => {
            const image = new Image();
            image.src = imageUrl;
            image.onload = () => {
                updateCanvasSize(image.width, image.height);
            };
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [jsonData]);

    return (
        <div style={{ width: '100%' }}>
            <canvas
                ref={canvasRef}
                style={{
                    width: '100%',
                    height: 'auto',
                }}
            />
            {selectedProductName && (
                <Tiles
                    selectedProductName={selectedProductName}
                    items={jsonData}
                />
            )}
        </div>
    );
};

export default ImageWithPolygon;
