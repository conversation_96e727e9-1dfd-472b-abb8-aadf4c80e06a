import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, <PERSON>alogA<PERSON>, Button } from '@mui/material';
import styles from './clique-password-dialog.module.scss';
import { useNavigate } from 'react-router-dom';
import { CLIQUE_PASSWORD } from '../../constants/urls';

interface CliquePasswordDialogProps {
    open: boolean;
    onClose: () => void;
}

const CliquePasswordDialog: React.FC<CliquePasswordDialogProps> = ({
    open,
    onClose,
}) => {
    const [password, setPassword] = useState<string>('');
    const [error, setError] = useState<string>('');
    const navigate = useNavigate();

    const handlePasswordChange = (
        event: React.ChangeEvent<HTMLInputElement>,
    ) => {
        setPassword(event.target.value);
        setError('');
    };

    const onConfirm = () => {
        if (password === CLIQUE_PASSWORD) {
            close();
            navigate('/shop-pages-clique');
        } else {
            setError('Incorrect password, please try again.');
        }
    };

    const close = () => {
        setPassword('');
        onClose();
    };

    return (
        <Dialog open={open} onClose={onClose}>
            <DialogContent>
                <div className={styles.container}>
                    <span className={styles.title}>
                        Enter the password to access the page
                    </span>
                    <input
                        type="password"
                        value={password}
                        onChange={handlePasswordChange}
                    />
                    {error && <span className={styles.error}>{error}</span>}
                </div>
            </DialogContent>
            <DialogActions>
                <Button onClick={close}>Cancel</Button>
                <Button onClick={onConfirm}>Continue</Button>
            </DialogActions>
        </Dialog>
    );
};

export default CliquePasswordDialog;
