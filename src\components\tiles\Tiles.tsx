import { useEffect, useState } from 'react';
import styles from './tiles.module.scss';
import { useGetUmbracoContentByTypeQuery } from '../../api/useGetUmbracoContentByTypeQuery';
import { useLocation } from 'react-router-dom';
import { API_URL } from '../../constants/urls';

type UmbraccoPhotoItem = {
    minatureImage: string;
    affiliateLink: string;
    productName: string;
    displayedName: string;
    brand: string;
};

interface TilesProps {
    selectedProductName: string | null;
    items: UmbraccoPhotoItem[];
}

function Tiles({ selectedProductName, items }: TilesProps) {
    return (
        <div className={styles.tiles}>
            {items
                .filter(item => {
                    const productName = item.displayedName.trim().toLowerCase();
                    const selectedName = selectedProductName
                        ?.trim()
                        .toLowerCase();
                    return productName === selectedName;
                })
                .map((item: UmbraccoPhotoItem, index: number) => {
                    const {
                        minatureImage,
                        affiliateLink,
                        productName,
                        brand,
                        displayedName,
                    } = item;

                    return (
                        <div className={styles.tile} key={index}>
                            <a
                                className={styles.href}
                                href={affiliateLink}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <div className={styles.minatureContainer}>
                                    <img
                                        className={styles.minature}
                                        src={minatureImage}
                                        alt={displayedName}
                                    />
                                </div>
                                <div className={styles.text}>
                                    <h2 className={styles.productName}>
                                        {displayedName}
                                    </h2>
                                    <p className={styles.brand}>{brand}</p>
                                </div>
                            </a>
                        </div>
                    );
                })}
        </div>
    );
}

export default Tiles;
