.container {
    max-width: 1540px;
    margin: 0 auto;
    background-color: white;
    position: relative;
    margin: -420px auto 0 auto;
    padding: 40px 60px 40px 60px;
}

.heroImage {
    background-image: url('../../assets/img/how-it-works.png');
    height: 100vh;
    min-height: 900px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center top;
    position: relative;
}

.info {
    display: flex;
    flex-wrap: wrap;
    gap: 90px;
}

@media (max-width: 768px) {
    .container {
        padding: 20px 20px 20px 20px;
    }
}
