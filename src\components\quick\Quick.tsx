import React from 'react';
import { useGetUmbraccoContentQuery } from '../../api/useGetUmbraccoContent';
import styles from './quick.module.scss';
import UpImage from '../../assets/img/up_arrow.png';
import Loader from '../loader/Loader';
import { API_URL } from '../../constants/urls';
type UmbraccoImage = {
    url: string;
};

function Quick() {
    const { data, error, isLoading } = useGetUmbraccoContentQuery(
        '35ffe33e-b320-4080-9ff6-d33e69a9d635',
    );

    const renderImage = (image: string | UmbraccoImage[]) => {
        if (Array.isArray(image) && image.length > 0) {
            return <img src={API_URL + image[0].url} alt="" />;
        }
        return null;
    };

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    if (isLoading) return <Loader />;
    if (error) return <p>Error loading data</p>;

    const {
        heading,
        paragraph,
        paragraph2,
        paragraph3,
        paragraph4,
        paragraph5,
        paragraph6,
        paragraph7,
        image,
        image2,
        image3,
        image4,
        image5,
        image6,
    } = data?.properties || {};

    return (
        <>
            <div
                className={styles.heading}
                dangerouslySetInnerHTML={{ __html: heading?.markup || '' }}
            />
            <div className={styles.columnContainer}>
                <div className={styles.column}>
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph?.markup || '',
                        }}
                    />
                    {renderImage(image)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph2?.markup || '',
                        }}
                    />
                    {renderImage(image2)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph3?.markup || '',
                        }}
                    />
                </div>
                <div className={styles.column}>
                    {renderImage(image3)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph4?.markup || '',
                        }}
                    />
                    {renderImage(image4)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph5?.markup || '',
                        }}
                    />
                    {renderImage(image5)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph6?.markup || '',
                        }}
                    />
                    {renderImage(image6)}
                    <div
                        className={styles.paragraph}
                        dangerouslySetInnerHTML={{
                            __html: paragraph7?.markup || '',
                        }}
                    />
                    <img
                        className={styles.arrow}
                        src={UpImage}
                        onClick={scrollToTop}
                    />
                    <hr className={styles.hr} />
                    <h3 className={styles.other}>OTHER CHATS</h3>
                    <h2 className={styles.author}>Leanne Hirsh Talks Style</h2>
                    <h2 className={styles.author}>
                        Donaldo Barros Talks Photography
                    </h2>
                </div>
            </div>
        </>
    );
}

export default Quick;
