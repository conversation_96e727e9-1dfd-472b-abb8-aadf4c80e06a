.heading > h1 {
    font-size: 28px;
    margin-left: 69px;
    font-family: 'source-sans-pro', sans-serif;
    font-weight: 600;
    font-style: normal;
}

.arrow:hover {
    cursor: pointer;
}

.paragraph {
    font-size: 20px;
    color: #5c6767;
    margin-bottom: 20px;
    font-family: 'source-sans-pro', sans-serif;
    font-weight: 400;
    font-style: normal;
}

.column {
    display: flex;
    flex-direction: column;
    max-width: 570px;
}

.columnContainer {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.column > img {
    margin-bottom: 21px;
}

.arrow {
    height: 46px;
    width: 50px;
}

.hr {
    width: 98%;
}

.other {
    font-size: 20px;
    font-family: 'source-sans-pro', sans-serif;
    font-weight: 600;
    font-style: normal;
}

.author {
    font-size: 20px;
    font-family: 'source-sans-pro', sans-serif;
    font-weight: 600;
    font-style: normal;
    color: #a3a3a3;
    margin-bottom: 5px;
    margin-top: 0px;
}

@media (max-width: 768px) {
    .heading > h1 {
        margin-left: 0px;
    }
}
