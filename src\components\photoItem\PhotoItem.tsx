import React from 'react';
import styles from './photo-item.module.scss';
import { Link } from 'react-router-dom';
import { API_URL } from '../../constants/urls';

interface PhotoItemProps {
    url: string;
    pageName: string;
}

function PhotoItem({ url, pageName }: PhotoItemProps) {
    const isVideo = url.endsWith('.mp4');

    return (
        <>
            <Link to={'/photos-content/' + pageName}>
                {isVideo ? (
                    <video
                        className={styles.video}
                        autoPlay
                        muted
                        playsInline
                        loop
                    >
                        <source src={API_URL + url} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                ) : (
                    <img src={API_URL + url} alt="media" />
                )}
            </Link>
            <p className={styles.margin}></p>
        </>
    );
}

export default PhotoItem;
