


// src/authConfig.js
export const msalConfig = {
  auth: {
    // 1. Application (client) ID z Azure Portal
    clientId: "3454000b-98d3-4b91-8491-ffb80b7111d6",

    // 2. Authority = adres logowania z konkretną policy
    authority: "https://traffiquedev.b2clogin.com/traffiquedev.onmicrosoft.com/B2C_1_traffiqueDev",

    // 3. Lista zaufanych domen 
    knownAuthorities: ["traffiquedev.b2clogin.com"],

    // 4. Gdzie ma wrócić przeglądarka po logowaniu
    redirectUri: "https://traffique.astroid.com.pl/",
  },

  cache: { cacheLocation: "sessionStorage" },
};

export const apiConfig = {
  // 1. Scope, który front ma poprosić
  scopes: ["api://3454000b-98d3-4b91-8491-ffb80b7111d6/umbraco.read"],

  // 2. <PERSON>res endpointu, który zwraca content
  //endpoint: "https://traffiquedev.azurewebsites.net/umbraco/delivery/api/v2/content/item/6595b171-21f8-40ad-9f71-5d8aa9ad0339",
};
