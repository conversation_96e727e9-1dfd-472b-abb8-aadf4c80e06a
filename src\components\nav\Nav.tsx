import React, { useState } from "react";
import styles from "./nav.module.scss";
import LogoImage from "../../assets/img/logo.png";
import LogoImageMobile from "../../assets/img/tr_symbol.jpg";
import SearchImage from "../../assets/img/search.png";
import ArrowImage from "../../assets/img/small_arrow.png";
import Facebook from "../../assets/img/facebook.png";
import Twitter from "../../assets/img/twitter.png";
import Youtube from "../../assets/img/yt.png";
import Instagram from "../../assets/img/instagram.png";
import Search2 from "../../assets/img/search2.png";
import ContentList from "../searchTest/search";
import CliquePasswordDialog from "../cliquePasswordDialog/clique-password-dialog";
import { useMsal } from "@azure/msal-react";
import { apiConfig } from "../../config/authConfig";

interface NavProps {
  currentPage: string;
}

const Nav: React.FC<NavProps> = ({ currentPage }) => {
  const [isMenuOpen, setMenuOpen] = useState(false);
  const [isSearchOpen, setSearchOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // 👉 MSAL state & actions
  const { instance, accounts } = useMsal();
  const isAuthenticated = accounts.length > 0;

  // const handleLogin = () => {
  // console.log("🔵 handleLogin fired");
  // instance.loginRedirect({ scopes: apiConfig.scopes });};

const handleLogin = async () => {
  console.log("🔵 handleLogin fired (popup test)");
  try {
    await instance.loginPopup({ scopes: ["openid"] });   // minimalny scope
    console.log("✅ popup wrócił, konto:", instance.getAllAccounts());
  } catch (err) {
    console.error("❌ popup error", err);
  }
};


  const handleLogout = () => instance.logoutRedirect();

  const openDialog = () => {
    setMenuOpen(false);
    setIsModalOpen(true);
  };
  const closeDialog = () => setIsModalOpen(false);

  const toggleMenu = () => {
    setMenuOpen(!isMenuOpen);
    setSearchOpen(false);
  };

  const toggleSearch = () => {
    setSearchOpen(!isSearchOpen);
    setMenuOpen(false);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  return (
    <header className={`${styles.lHeader} ${isMenuOpen ? styles.sticky : ""}`}>
      <nav className={`${styles.nav} ${styles.bdGrid}`}>
        {/* Logo */}
        <div>
          <a href="/" className={styles.nav__logo}>
            <img className={styles.logoDesktop} src={LogoImage} alt="logo" />
            <img className={styles.logoMobile} src={LogoImageMobile} alt="logo" />
          </a>
        </div>

        {/* Desktop search */}
        <div className={styles.searchingDesktop} onClick={toggleSearch}>
          <img src={Search2} alt="Search" className={styles.searchImage} />
          <input
            name="search"
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            id={styles.search}
            placeholder="Search anytime by typing"
          />
        </div>

        {/* Main menu */}
        <div
          className={`${styles.nav__menu} ${styles.menu} ${isMenuOpen ? styles.show : ""}`}
          id={styles.navMenu}
        >
          <ul className={styles.nav__list}>
            <li className={styles.nav__item}>
              <a
                href="/"
                className={`${styles.nav__link} ${currentPage === "home" ? styles.activeLink : ""}`}
              >
                Home
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                href="/about-us"
                className={`${styles.nav__link} ${currentPage === "about-us" ? styles.activeLink : ""}`}
              >
                About Us
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                href="/how-it-works"
                className={`${styles.nav__link} ${currentPage === "how-it-works" ? styles.activeLink : ""}`}
              >
                How It Works
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                href="/quick-chats"
                className={`${styles.nav__link} ${currentPage === "quick-chats" ? styles.activeLink : ""}`}
              >
                Quick Chats
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                href="/photographers"
                className={`${styles.nav__link} ${currentPage === "photographers" ? styles.activeLink : ""}`}
              >
                Photographers
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                onClick={openDialog}
                className={`${styles.nav__link} ${currentPage === "clique" ? styles.activeLink : ""}`}
                style={{ cursor: "pointer" }}
              >
                Clique
              </a>
            </li>
            <li className={styles.nav__item}>
              <a
                href="/form"
                className={`${styles.nav__link} ${currentPage === "form" ? styles.activeLink : ""}`}
              >
                Form
              </a>
            </li>
            {/* 🔐 Login / Logout */}
            <li className={styles.nav__item}>
              {isAuthenticated ? (
                <a
                  onClick={handleLogout}
                  className={styles.nav__link}
                  style={{ cursor: "pointer" }}
                >
                  Logout
                </a>
              ) : (
                // <a
                //   onClick={handleLogin}
                //   className={styles.nav__link}
                //   style={{ cursor: "pointer" }}
                // >
                //   Login
                // </a>
                  <button
                    onClick={handleLogin}
                    className={styles.nav__link}
                    style={{ cursor: "pointer", background: "none", border: 0 }}
                    type="button">Login
                  </button>
              )}
            </li>
            {/* …Friends of Traffique, newsletter, social icons remain unchanged … */}
          </ul>
        </div>

        {/* Search menu for mobile */}
        <div
          className={`${styles.nav__menu} ${styles.menu} ${styles.searchMenu} ${
            isSearchOpen ? styles.show : ""
          }`}
          id={styles.navMenu}
        >
          <ContentList searchTerm={searchTerm} />
        </div>

        {/* Buttons (search toggle & hamburger) */}
        <div className={styles.buttons}>
          <div onClick={toggleSearch}>
            {isSearchOpen ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className={styles.closeIcon}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <img className={styles.searchIcon} src={Search2} />
            )}
          </div>
          <div
            className={styles.nav__toggle}
            id={styles.navToggle}
            onClick={toggleMenu}
          >
            {isMenuOpen ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className={styles.closeIcon}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className={styles.hamburgerIcon}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </div>
        </div>

        {/* Clique dialog */}
        <CliquePasswordDialog open={isModalOpen} onClose={closeDialog} />
      </nav>
    </header>
  );
};

export default Nav;
