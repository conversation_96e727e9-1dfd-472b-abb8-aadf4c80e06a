import axios from 'axios';
import { useQuery, UseQueryOptions } from 'react-query';
import { API_URL } from '../constants/urls';

export const useGetUmbracoFormsQuery = (
    contentId: string,
    options?: UseQueryOptions<any, any>,
) => {
    const getUmbracoForms = async (): Promise<any> => {
        try {
            const apiKey = 'Q0dvfM6y9uR3Gf8P1zF2I6Khj7L8n9MbQ3P6s0T3Z4W7t2X3R4';

            const response = await axios.get<any>(
                `${API_URL}umbraco/forms/api/v1/definitions/${contentId}`,
                {
                    headers: {
                        'Api-Key': apiKey,
                    },
                },
            );

            return response.data;
        } catch (error: any) {
            const status = error.response?.status || 500;
            throw new Error(
                `Error fetching content with id ${contentId}: ${status}`,
            );
        }
    };

    return useQuery<any, any>(['getUmbracoForms', contentId], getUmbracoForms, {
        ...options,
        enabled: !!contentId,
    });
};
