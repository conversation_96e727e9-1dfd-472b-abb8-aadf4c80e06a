.tile {
    width: 26vw;
    max-width: 90px;
    max-height: 90px;
    height: 26vw;
    background-color: #fff;
    border-radius: 50%;
}

.href {
    display: flex;
    flex-direction: column;
    height: 100%;

    .minatureContainer {
        padding: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-grow: 1;
        overflow: hidden;
        border-radius: 50%;

        .minature {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
    }

    .text {
        padding: 3px;
        text-align: center;
        flex-shrink: 0;

        h2 {
            margin: 0;
            text-transform: uppercase;
            font-weight: 400;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        p {
            margin: 0;
            margin-top: -5px;
            text-transform: uppercase;
            font-weight: 600;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
    }
}
