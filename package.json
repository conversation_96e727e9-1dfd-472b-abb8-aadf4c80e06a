{"name": "traffique-frontend", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^4.13.0", "@azure/msal-react": "^3.0.12", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.96", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.8", "commonninja-react": "^1.1.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-player": "^2.16.0", "react-query": "^3.39.3", "react-responsive": "^10.0.0", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "react-slick": "^0.30.2", "react-spinners": "^0.14.1", "react-swipeable": "^7.0.1", "sass": "^1.77.2", "slick-carousel": "^1.8.1", "typescript": "^4.9.5", "video.js": "^8.19.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/js": "^9.1.1", "@types/lodash": "^4.17.7", "@types/react-slick": "^0.23.13", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "globals": "^15.0.0", "prettier": "3.3.3", "typescript-eslint": "^7.7.1"}}